# Todo Web Application

A full-stack Todo application built with React + Shadcn UI frontend and Python FastAPI backend, using SQLite for local data storage.

## Features

- ✅ Create, read, update, and delete todos
- ✅ Mark todos as complete/incomplete
- ✅ Clean, responsive UI with Shadcn UI components
- ✅ Real-time error handling
- ✅ SQLite database for persistent storage
- ✅ RESTful API with FastAPI

## Tech Stack

### Frontend
- React 18
- Vite (build tool)
- Shadcn UI (component library)
- Tailwind CSS (styling)
- Lucide React (icons)

### Backend
- Python 3.8+
- FastAPI (web framework)
- SQLAlchemy (ORM)
- SQLite (database)
- Pydantic (data validation)

## Setup Instructions

### Prerequisites
- Python 3.8 or higher
- Node.js 16 or higher
- npm or yarn

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   ```

3. Activate the virtual environment:
   - Windows:
     ```bash
     venv\Scripts\activate
     ```
   - macOS/Linux:
     ```bash
     source venv/bin/activate
     ```

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

5. Start the backend server:
   ```bash
   python main.py
   ```

   The API will be available at `http://localhost:8000`
   API documentation will be available at `http://localhost:8000/docs`

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:5173`

## Usage

1. Start both the backend and frontend servers as described above
2. Open your browser and navigate to `http://localhost:5173`
3. Add new todos using the form at the top
4. Click the checkbox to mark todos as complete/incomplete
5. Use the edit button (pencil icon) to modify existing todos
6. Use the delete button (trash icon) to remove todos

## API Endpoints

- `GET /todos` - Get all todos
- `POST /todos` - Create a new todo
- `GET /todos/{id}` - Get a specific todo
- `PUT /todos/{id}` - Update a todo
- `DELETE /todos/{id}` - Delete a todo

## Database

The application uses SQLite database (`todos.db`) which will be automatically created in the backend directory when you first run the server.

## Project Structure

```
Assignment/
├── backend/
│   ├── main.py              # FastAPI application
│   ├── models.py            # Pydantic models
│   ├── database.py          # Database configuration
│   ├── requirements.txt     # Python dependencies
│   └── todos.db            # SQLite database (created automatically)
├── frontend/
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API service
│   │   ├── lib/           # Utility functions
│   │   └── App.jsx        # Main application component
│   ├── package.json       # Node.js dependencies
│   └── ...
└── README.md              # This file
```

## Development Notes

- The backend runs on port 8000
- The frontend runs on port 5173 (Vite default)
- CORS is configured to allow requests from the frontend
- The database is automatically created and tables are set up on first run
- All API responses include proper error handling and status codes
