const API_BASE_URL = 'http://localhost:8000'

class ApiError extends Error {
  constructor(message, status) {
    super(message)
    this.status = status
  }
}

async function handleResponse(response) {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }))
    throw new ApiError(errorData.detail || 'Request failed', response.status)
  }
  
  if (response.status === 204) {
    return null
  }
  
  return response.json()
}

export const todoApi = {
  // Get all todos
  async getTodos() {
    const response = await fetch(`${API_BASE_URL}/todos`)
    return handleResponse(response)
  },

  // Create a new todo
  async createTodo(todo) {
    const response = await fetch(`${API_BASE_URL}/todos`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(todo),
    })
    return handleResponse(response)
  },

  // Update a todo
  async updateTodo(id, todo) {
    const response = await fetch(`${API_BASE_URL}/todos/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(todo),
    })
    return handleResponse(response)
  },

  // Delete a todo
  async deleteTodo(id) {
    const response = await fetch(`${API_BASE_URL}/todos/${id}`, {
      method: 'DELETE',
    })
    return handleResponse(response)
  },
}
